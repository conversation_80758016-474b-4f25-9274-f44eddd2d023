{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://trafficdirector.googleapis.com/", "batchPath": "batch", "canonicalName": "Traffic Director Service", "description": "", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/traffic-director", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "trafficdirector:v3", "kind": "discovery#restDescription", "mtlsRootUrl": "https://trafficdirector.mtls.googleapis.com/", "name": "trafficdirector", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"discovery": {"methods": {"client_status": {"description": "", "flatPath": "v3/discovery:client_status", "httpMethod": "POST", "id": "trafficdirector.discovery.client_status", "parameterOrder": [], "parameters": {}, "path": "v3/discovery:client_status", "request": {"$ref": "ClientStatusRequest"}, "response": {"$ref": "ClientStatusResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}, "revision": "20250618", "rootUrl": "https://trafficdirector.googleapis.com/", "schemas": {"Address": {"description": "Addresses specify either a logical or physical address and port, which are used to tell Envoy where to bind/listen, connect to upstream and find management servers.", "id": "Address", "properties": {"envoyInternalAddress": {"$ref": "EnvoyInternalAddress", "description": "Specifies a user-space address handled by :ref:`internal listeners `."}, "pipe": {"$ref": "<PERSON><PERSON>"}, "socketAddress": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "type": "object"}, "BuildVersion": {"description": "BuildVersion combines SemVer version of extension with free-form build information (i.e. 'alpha', 'private-build') as a set of strings.", "id": "BuildVersion", "properties": {"metadata": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Free-form build information. Envoy defines several well known keys in the source/common/version/version.h file", "type": "object"}, "version": {"$ref": "SemanticVersion", "description": "SemVer version of extension."}}, "type": "object"}, "ClientConfig": {"description": "All xds configs for a particular client.", "id": "ClientConfig", "properties": {"clientScope": {"description": "For xDS clients, the scope in which the data is used. For example, gRPC indicates the data plane target or that the data is associated with gRPC server(s).", "type": "string"}, "genericXdsConfigs": {"description": "Represents generic xDS config and the exact config structure depends on the type URL (like Cluster if it is CDS)", "items": {"$ref": "GenericXdsConfig"}, "type": "array"}, "node": {"$ref": "Node", "description": "Node for a particular client."}, "xdsConfig": {"deprecated": true, "description": "This field is deprecated in favor of generic_xds_configs which is much simpler and uniform in structure.", "items": {"$ref": "PerXdsConfig"}, "type": "array"}}, "type": "object"}, "ClientStatusRequest": {"description": "Request for client status of clients identified by a list of NodeMatchers.", "id": "ClientStatusRequest", "properties": {"excludeResourceContents": {"description": "If true, the server will not include the resource contents in the response (i.e., the generic_xds_configs.xds_config field will not be populated). [#not-implemented-hide:]", "type": "boolean"}, "node": {"$ref": "Node", "description": "The node making the csds request."}, "nodeMatchers": {"description": "Management server can use these match criteria to identify clients. The match follows OR semantics.", "items": {"$ref": "NodeMatcher"}, "type": "array"}}, "type": "object"}, "ClientStatusResponse": {"id": "ClientStatusResponse", "properties": {"config": {"description": "Client configs for the clients specified in the ClientStatusRequest.", "items": {"$ref": "ClientConfig"}, "type": "array"}}, "type": "object"}, "ClustersConfigDump": {"description": "Envoy's cluster manager fills this message with all currently known clusters. Cluster configuration information can be used to recreate an Envoy configuration by populating all clusters as static clusters or by returning them in a CDS response.", "id": "ClustersConfigDump", "properties": {"dynamicActiveClusters": {"description": "The dynamically loaded active clusters. These are clusters that are available to service data plane traffic.", "items": {"$ref": "DynamicCluster"}, "type": "array"}, "dynamicWarmingClusters": {"description": "The dynamically loaded warming clusters. These are clusters that are currently undergoing warming in preparation to service data plane traffic. Note that if attempting to recreate an Envoy configuration from a configuration dump, the warming clusters should generally be discarded.", "items": {"$ref": "DynamicCluster"}, "type": "array"}, "staticClusters": {"description": "The statically loaded cluster configs.", "items": {"$ref": "StaticCluster"}, "type": "array"}, "versionInfo": {"description": "This is the :ref:`version_info ` in the last processed CDS discovery response. If there are only static bootstrap clusters, this field will be \"\".", "type": "string"}}, "type": "object"}, "ContextParams": {"description": "Additional parameters that can be used to select resource variants. These include any global context parameters, per-resource type client feature capabilities and per-resource type functional attributes. All per-resource type attributes will be `xds.resource.` prefixed and some of these are documented below: `xds.resource.listening_address`: The value is \"IP:port\" (e.g. \"********:8080\") which is the listening address of a Listener. Used in a Listener resource query.", "id": "ContextParams", "properties": {"params": {"additionalProperties": {"type": "string"}, "type": "object"}}, "type": "object"}, "DoubleMatcher": {"description": "Specifies the way to match a double value.", "id": "DoubleM<PERSON>er", "properties": {"exact": {"description": "If specified, the input double value must be equal to the value specified here.", "format": "double", "type": "number"}, "range": {"$ref": "DoubleRange", "description": "If specified, the input double value must be in the range specified here. Note: The range is using half-open interval semantics [start, end)."}}, "type": "object"}, "DoubleRange": {"description": "Specifies the double start and end of the range using half-open interval semantics [start, end).", "id": "DoubleRange", "properties": {"end": {"description": "end of the range (exclusive)", "format": "double", "type": "number"}, "start": {"description": "start of the range (inclusive)", "format": "double", "type": "number"}}, "type": "object"}, "DynamicCluster": {"description": "Describes a dynamically loaded cluster via the CDS API. [#next-free-field: 6]", "id": "DynamicCluster", "properties": {"clientStatus": {"description": "The client status of this resource. [#not-implemented-hide:]", "enum": ["UNKNOWN", "REQUESTED", "DOES_NOT_EXIST", "ACKED", "NACKED", "RECEIVED_ERROR", "TIMEOUT"], "enumDescriptions": ["Resource status is not available/unknown.", "Client requested this resource but hasn't received any update from management server. The client will not fail requests, but will queue them until update arrives or the client times out waiting for the resource.", "This resource has been requested by the client but has either not been delivered by the server or was previously delivered by the server and then subsequently removed from resources provided by the server. For more information, please refer to the :ref:`\"Knowing When a Requested Resource Does Not Exist\" ` section.", "Client received this resource and replied with ACK.", "Client received this resource and replied with NACK.", "Client received an error from the control plane. The attached config dump is the most recent accepted one. If no config is accepted yet, the attached config dump will be empty.", "Client timed out waiting for the resource from the control plane."], "type": "string"}, "cluster": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The cluster config.", "type": "object"}, "errorState": {"$ref": "UpdateFailureState", "description": "Set if the last update failed, cleared after the next successful update. The ``error_state`` field contains the rejected version of this particular resource along with the reason and timestamp. For successfully updated or acknowledged resource, this field should be empty. [#not-implemented-hide:]"}, "lastUpdated": {"description": "The timestamp when the Cluster was last updated.", "format": "google-datetime", "type": "string"}, "versionInfo": {"description": "This is the per-resource version information. This version is currently taken from the :ref:`version_info ` field at the time that the cluster was loaded. In the future, discrete per-cluster versions may be supported by the API.", "type": "string"}}, "type": "object"}, "DynamicEndpointConfig": {"description": "[#next-free-field: 6]", "id": "DynamicEndpointConfig", "properties": {"clientStatus": {"description": "The client status of this resource. [#not-implemented-hide:]", "enum": ["UNKNOWN", "REQUESTED", "DOES_NOT_EXIST", "ACKED", "NACKED", "RECEIVED_ERROR", "TIMEOUT"], "enumDescriptions": ["Resource status is not available/unknown.", "Client requested this resource but hasn't received any update from management server. The client will not fail requests, but will queue them until update arrives or the client times out waiting for the resource.", "This resource has been requested by the client but has either not been delivered by the server or was previously delivered by the server and then subsequently removed from resources provided by the server. For more information, please refer to the :ref:`\"Knowing When a Requested Resource Does Not Exist\" ` section.", "Client received this resource and replied with ACK.", "Client received this resource and replied with NACK.", "Client received an error from the control plane. The attached config dump is the most recent accepted one. If no config is accepted yet, the attached config dump will be empty.", "Client timed out waiting for the resource from the control plane."], "type": "string"}, "endpointConfig": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The endpoint config.", "type": "object"}, "errorState": {"$ref": "UpdateFailureState", "description": "Set if the last update failed, cleared after the next successful update. The ``error_state`` field contains the rejected version of this particular resource along with the reason and timestamp. For successfully updated or acknowledged resource, this field should be empty. [#not-implemented-hide:]"}, "lastUpdated": {"description": "[#not-implemented-hide:] The timestamp when the Endpoint was last updated.", "format": "google-datetime", "type": "string"}, "versionInfo": {"description": "[#not-implemented-hide:] This is the per-resource version information. This version is currently taken from the :ref:`version_info ` field at the time that the endpoint configuration was loaded.", "type": "string"}}, "type": "object"}, "DynamicListener": {"description": "Describes a dynamically loaded listener via the LDS API. [#next-free-field: 7]", "id": "DynamicListener", "properties": {"activeState": {"$ref": "DynamicListenerState", "description": "The listener state for any active listener by this name. These are listeners that are available to service data plane traffic."}, "clientStatus": {"description": "The client status of this resource. [#not-implemented-hide:]", "enum": ["UNKNOWN", "REQUESTED", "DOES_NOT_EXIST", "ACKED", "NACKED", "RECEIVED_ERROR", "TIMEOUT"], "enumDescriptions": ["Resource status is not available/unknown.", "Client requested this resource but hasn't received any update from management server. The client will not fail requests, but will queue them until update arrives or the client times out waiting for the resource.", "This resource has been requested by the client but has either not been delivered by the server or was previously delivered by the server and then subsequently removed from resources provided by the server. For more information, please refer to the :ref:`\"Knowing When a Requested Resource Does Not Exist\" ` section.", "Client received this resource and replied with ACK.", "Client received this resource and replied with NACK.", "Client received an error from the control plane. The attached config dump is the most recent accepted one. If no config is accepted yet, the attached config dump will be empty.", "Client timed out waiting for the resource from the control plane."], "type": "string"}, "drainingState": {"$ref": "DynamicListenerState", "description": "The listener state for any draining listener by this name. These are listeners that are currently undergoing draining in preparation to stop servicing data plane traffic. Note that if attempting to recreate an Envoy configuration from a configuration dump, the draining listeners should generally be discarded."}, "errorState": {"$ref": "UpdateFailureState", "description": "Set if the last update failed, cleared after the next successful update. The ``error_state`` field contains the rejected version of this particular resource along with the reason and timestamp. For successfully updated or acknowledged resource, this field should be empty."}, "name": {"description": "The name or unique id of this listener, pulled from the DynamicListenerState config.", "type": "string"}, "warmingState": {"$ref": "DynamicListenerState", "description": "The listener state for any warming listener by this name. These are listeners that are currently undergoing warming in preparation to service data plane traffic. Note that if attempting to recreate an Envoy configuration from a configuration dump, the warming listeners should generally be discarded."}}, "type": "object"}, "DynamicListenerState": {"id": "DynamicListenerState", "properties": {"lastUpdated": {"description": "The timestamp when the Listener was last successfully updated.", "format": "google-datetime", "type": "string"}, "listener": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The listener config.", "type": "object"}, "versionInfo": {"description": "This is the per-resource version information. This version is currently taken from the :ref:`version_info ` field at the time that the listener was loaded. In the future, discrete per-listener versions may be supported by the API.", "type": "string"}}, "type": "object"}, "DynamicRouteConfig": {"description": "[#next-free-field: 6]", "id": "DynamicRouteConfig", "properties": {"clientStatus": {"description": "The client status of this resource. [#not-implemented-hide:]", "enum": ["UNKNOWN", "REQUESTED", "DOES_NOT_EXIST", "ACKED", "NACKED", "RECEIVED_ERROR", "TIMEOUT"], "enumDescriptions": ["Resource status is not available/unknown.", "Client requested this resource but hasn't received any update from management server. The client will not fail requests, but will queue them until update arrives or the client times out waiting for the resource.", "This resource has been requested by the client but has either not been delivered by the server or was previously delivered by the server and then subsequently removed from resources provided by the server. For more information, please refer to the :ref:`\"Knowing When a Requested Resource Does Not Exist\" ` section.", "Client received this resource and replied with ACK.", "Client received this resource and replied with NACK.", "Client received an error from the control plane. The attached config dump is the most recent accepted one. If no config is accepted yet, the attached config dump will be empty.", "Client timed out waiting for the resource from the control plane."], "type": "string"}, "errorState": {"$ref": "UpdateFailureState", "description": "Set if the last update failed, cleared after the next successful update. The ``error_state`` field contains the rejected version of this particular resource along with the reason and timestamp. For successfully updated or acknowledged resource, this field should be empty. [#not-implemented-hide:]"}, "lastUpdated": {"description": "The timestamp when the Route was last updated.", "format": "google-datetime", "type": "string"}, "routeConfig": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The route config.", "type": "object"}, "versionInfo": {"description": "This is the per-resource version information. This version is currently taken from the :ref:`version_info ` field at the time that the route configuration was loaded.", "type": "string"}}, "type": "object"}, "DynamicScopedRouteConfigs": {"description": "[#next-free-field: 7]", "id": "DynamicScopedRouteConfigs", "properties": {"clientStatus": {"description": "The client status of this resource. [#not-implemented-hide:]", "enum": ["UNKNOWN", "REQUESTED", "DOES_NOT_EXIST", "ACKED", "NACKED", "RECEIVED_ERROR", "TIMEOUT"], "enumDescriptions": ["Resource status is not available/unknown.", "Client requested this resource but hasn't received any update from management server. The client will not fail requests, but will queue them until update arrives or the client times out waiting for the resource.", "This resource has been requested by the client but has either not been delivered by the server or was previously delivered by the server and then subsequently removed from resources provided by the server. For more information, please refer to the :ref:`\"Knowing When a Requested Resource Does Not Exist\" ` section.", "Client received this resource and replied with ACK.", "Client received this resource and replied with NACK.", "Client received an error from the control plane. The attached config dump is the most recent accepted one. If no config is accepted yet, the attached config dump will be empty.", "Client timed out waiting for the resource from the control plane."], "type": "string"}, "errorState": {"$ref": "UpdateFailureState", "description": "Set if the last update failed, cleared after the next successful update. The ``error_state`` field contains the rejected version of this particular resource along with the reason and timestamp. For successfully updated or acknowledged resource, this field should be empty. [#not-implemented-hide:]"}, "lastUpdated": {"description": "The timestamp when the scoped route config set was last updated.", "format": "google-datetime", "type": "string"}, "name": {"description": "The name assigned to the scoped route configurations.", "type": "string"}, "scopedRouteConfigs": {"description": "The scoped route configurations.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "versionInfo": {"description": "This is the per-resource version information. This version is currently taken from the :ref:`version_info ` field at the time that the scoped routes configuration was loaded.", "type": "string"}}, "type": "object"}, "EndpointsConfigDump": {"description": "Envoy's admin fill this message with all currently known endpoints. Endpoint configuration information can be used to recreate an Envoy configuration by populating all endpoints as static endpoints or by returning them in an EDS response.", "id": "EndpointsConfigDump", "properties": {"dynamicEndpointConfigs": {"description": "The dynamically loaded endpoint configs.", "items": {"$ref": "DynamicEndpointConfig"}, "type": "array"}, "staticEndpointConfigs": {"description": "The statically loaded endpoint configs.", "items": {"$ref": "StaticEndpointConfig"}, "type": "array"}}, "type": "object"}, "EnvoyInternalAddress": {"description": "The address represents an envoy internal listener. [#comment: ", "id": "EnvoyInternalAddress", "properties": {"endpointId": {"description": "Specifies an endpoint identifier to distinguish between multiple endpoints for the same internal listener in a single upstream pool. Only used in the upstream addresses for tracking changes to individual endpoints. This, for example, may be set to the final destination IP for the target internal listener.", "type": "string"}, "serverListenerName": {"description": "Specifies the :ref:`name ` of the internal listener.", "type": "string"}}, "type": "object"}, "Extension": {"description": "Version and identification for an Envoy extension. [#next-free-field: 7]", "id": "Extension", "properties": {"category": {"description": "Category of the extension. Extension category names use reverse DNS notation. For instance \"envoy.filters.listener\" for Envoy's built-in listener filters or \"com.acme.filters.http\" for HTTP filters from acme.com vendor. [#comment:", "type": "string"}, "disabled": {"description": "Indicates that the extension is present but was disabled via dynamic configuration.", "type": "boolean"}, "name": {"description": "This is the name of the Envoy filter as specified in the Envoy configuration, e.g. envoy.filters.http.router, com.acme.widget.", "type": "string"}, "typeDescriptor": {"deprecated": true, "description": "[#not-implemented-hide:] Type descriptor of extension configuration proto. [#comment:", "type": "string"}, "typeUrls": {"description": "Type URLs of extension configuration protos.", "items": {"type": "string"}, "type": "array"}, "version": {"$ref": "BuildVersion", "description": "The version is a property of the extension and maintained independently of other extensions and the Envoy API. This field is not set when extension did not provide version information."}}, "type": "object"}, "GenericXdsConfig": {"description": "GenericXdsConfig is used to specify the config status and the dump of any xDS resource identified by their type URL. It is the generalized version of the now deprecated ListenersConfigDump, ClustersConfigDump etc [#next-free-field: 10]", "id": "GenericXdsConfig", "properties": {"clientStatus": {"description": "Per xDS resource status from the view of a xDS client", "enum": ["UNKNOWN", "REQUESTED", "DOES_NOT_EXIST", "ACKED", "NACKED", "RECEIVED_ERROR", "TIMEOUT"], "enumDescriptions": ["Resource status is not available/unknown.", "Client requested this resource but hasn't received any update from management server. The client will not fail requests, but will queue them until update arrives or the client times out waiting for the resource.", "This resource has been requested by the client but has either not been delivered by the server or was previously delivered by the server and then subsequently removed from resources provided by the server. For more information, please refer to the :ref:`\"Knowing When a Requested Resource Does Not Exist\" ` section.", "Client received this resource and replied with ACK.", "Client received this resource and replied with NACK.", "Client received an error from the control plane. The attached config dump is the most recent accepted one. If no config is accepted yet, the attached config dump will be empty.", "Client timed out waiting for the resource from the control plane."], "type": "string"}, "configStatus": {"description": "Per xDS resource config status. It is generated by management servers. It will not be present if the CSDS server is an xDS client.", "enum": ["UNKNOWN", "SYNCED", "NOT_SENT", "STALE", "ERROR"], "enumDescriptions": ["Status info is not available/unknown.", "Management server has sent the config to client and received ACK.", "Config is not sent.", "Management server has sent the config to client but hasn’t received ACK/NACK.", "Management server has sent the config to client but received NACK. The attached config dump will be the latest config (the rejected one), since it is the persisted version in the management server."], "type": "string"}, "errorState": {"$ref": "UpdateFailureState", "description": "Set if the last update failed, cleared after the next successful update. The *error_state* field contains the rejected version of this particular resource along with the reason and timestamp. For successfully updated or acknowledged resource, this field should be empty. [#not-implemented-hide:]"}, "isStaticResource": {"description": "Is static resource is true if it is specified in the config supplied through the file at the startup.", "type": "boolean"}, "lastUpdated": {"description": "Timestamp when the xDS resource was last updated", "format": "google-datetime", "type": "string"}, "name": {"description": "Name of the xDS resource", "type": "string"}, "typeUrl": {"description": "Type_url represents the fully qualified name of xDS resource type like envoy.v3.Cluster, envoy.v3.ClusterLoadAssignment etc.", "type": "string"}, "versionInfo": {"description": "This is the :ref:`version_info ` in the last processed xDS discovery response. If there are only static bootstrap listeners, this field will be \"\"", "type": "string"}, "xdsConfig": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The xDS resource config. Actual content depends on the type", "type": "object"}}, "type": "object"}, "GoogleRE2": {"description": "Google's `RE2 `_ regex engine. The regex string must adhere to the documented `syntax `_. The engine is designed to complete execution in linear time as well as limit the amount of memory used. Envoy supports program size checking via runtime. The runtime keys ``re2.max_program_size.error_level`` and ``re2.max_program_size.warn_level`` can be set to integers as the maximum program size or complexity that a compiled regex can have before an exception is thrown or a warning is logged, respectively. ``re2.max_program_size.error_level`` defaults to 100, and ``re2.max_program_size.warn_level`` has no default if unset (will not check/log a warning). Envoy emits two stats for tracking the program size of regexes: the histogram ``re2.program_size``, which records the program size, and the counter ``re2.exceeded_warn_level``, which is incremented each time the program size exceeds the warn level threshold.", "id": "GoogleRE2", "properties": {"maxProgramSize": {"deprecated": true, "description": "This field controls the RE2 \"program size\" which is a rough estimate of how complex a compiled regex is to evaluate. A regex that has a program size greater than the configured value will fail to compile. In this case, the configured max program size can be increased or the regex can be simplified. If not specified, the default is 100. This field is deprecated; regexp validation should be performed on the management server instead of being done by each individual client. .. note:: Although this field is deprecated, the program size will still be checked against the global ``re2.max_program_size.error_level`` runtime value.", "format": "uint32", "type": "integer"}}, "type": "object"}, "InlineScopedRouteConfigs": {"id": "InlineScopedRouteConfigs", "properties": {"lastUpdated": {"description": "The timestamp when the scoped route config set was last updated.", "format": "google-datetime", "type": "string"}, "name": {"description": "The name assigned to the scoped route configurations.", "type": "string"}, "scopedRouteConfigs": {"description": "The scoped route configurations.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}}, "type": "object"}, "ListMatcher": {"description": "Specifies the way to match a list value.", "id": "ListMatcher", "properties": {"oneOf": {"$ref": "ValueMatcher", "description": "If specified, at least one of the values in the list must match the value specified."}}, "type": "object"}, "ListenersConfigDump": {"description": "Envoy's listener manager fills this message with all currently known listeners. Listener configuration information can be used to recreate an Envoy configuration by populating all listeners as static listeners or by returning them in a LDS response.", "id": "ListenersConfigDump", "properties": {"dynamicListeners": {"description": "State for any warming, active, or draining listeners.", "items": {"$ref": "DynamicListener"}, "type": "array"}, "staticListeners": {"description": "The statically loaded listener configs.", "items": {"$ref": "StaticListener"}, "type": "array"}, "versionInfo": {"description": "This is the :ref:`version_info ` in the last processed LDS discovery response. If there are only static bootstrap listeners, this field will be \"\".", "type": "string"}}, "type": "object"}, "Locality": {"description": "Identifies location of where either <PERSON><PERSON> runs or where upstream hosts run.", "id": "Locality", "properties": {"region": {"description": "Region this :ref:`zone ` belongs to.", "type": "string"}, "subZone": {"description": "When used for locality of upstream hosts, this field further splits zone into smaller chunks of sub-zones so they can be load balanced independently.", "type": "string"}, "zone": {"description": "Defines the local service zone where <PERSON><PERSON> is running. Though optional, it should be set if discovery service routing is used and the discovery service exposes :ref:`zone data `, either in this message or via :option:`--service-zone`. The meaning of zone is context dependent, e.g. `Availability Zone (AZ) `_ on AWS, `Zone `_ on GCP, etc.", "type": "string"}}, "type": "object"}, "Node": {"description": "Identifies a specific Envoy instance. The node identifier is presented to the management server, which may use this identifier to distinguish per Envoy configuration for serving. [#next-free-field: 13]", "id": "Node", "properties": {"clientFeatures": {"description": "Client feature support list. These are well known features described in the Envoy API repository for a given major version of an API. Client features use reverse DNS naming scheme, for example ``com.acme.feature``. See :ref:`the list of features ` that xDS client may support.", "items": {"type": "string"}, "type": "array"}, "cluster": {"description": "Defines the local service cluster name where <PERSON><PERSON> is running. Though optional, it should be set if any of the following features are used: :ref:`statsd `, :ref:`health check cluster verification `, :ref:`runtime override directory `, :ref:`user agent addition `, :ref:`HTTP global rate limiting `, :ref:`CDS `, and :ref:`HTTP tracing `, either in this message or via :option:`--service-cluster`.", "type": "string"}, "dynamicParameters": {"additionalProperties": {"$ref": "ContextParams"}, "description": "Map from xDS resource type URL to dynamic context parameters. These may vary at runtime (unlike other fields in this message). For example, the xDS client may have a shard identifier that changes during the lifetime of the xDS client. In Envoy, this would be achieved by updating the dynamic context on the Server::Instance's LocalInfo context provider. The shard ID dynamic parameter then appears in this field during future discovery requests.", "type": "object"}, "extensions": {"description": "List of extensions and their versions supported by the node.", "items": {"$ref": "Extension"}, "type": "array"}, "id": {"description": "An opaque node identifier for the Envoy node. This also provides the local service node name. It should be set if any of the following features are used: :ref:`statsd `, :ref:`CDS `, and :ref:`HTTP tracing `, either in this message or via :option:`--service-node`.", "type": "string"}, "listeningAddresses": {"deprecated": true, "description": "Known listening ports on the node as a generic hint to the management server for filtering :ref:`listeners ` to be returned. For example, if there is a listener bound to port 80, the list can optionally contain the SocketAddress ``(0.0.0.0,80)``. The field is optional and just a hint.", "items": {"$ref": "Address"}, "type": "array"}, "locality": {"$ref": "Locality", "description": "Locality specifying where the Envoy instance is running."}, "metadata": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Opaque metadata extending the node identifier. Envoy will pass this directly to the management server.", "type": "object"}, "userAgentBuildVersion": {"$ref": "BuildVersion", "description": "Structured version of the entity requesting config."}, "userAgentName": {"description": "Free-form string that identifies the entity requesting config. E.g. \"envoy\" or \"grpc\"", "type": "string"}, "userAgentVersion": {"description": "Free-form string that identifies the version of the entity requesting config. E.g. \"1.12.2\" or \"abcd1234\", or \"SpecialEnvoyBuild\"", "type": "string"}}, "type": "object"}, "NodeMatcher": {"description": "Specifies the way to match a Node. The match follows AND semantics.", "id": "NodeMatcher", "properties": {"nodeId": {"$ref": "StringMatcher", "description": "Specifies match criteria on the node id."}, "nodeMetadatas": {"description": "Specifies match criteria on the node metadata.", "items": {"$ref": "StructMatcher"}, "type": "array"}}, "type": "object"}, "NullMatch": {"description": "NullMatch is an empty message to specify a null value.", "id": "NullMatch", "properties": {}, "type": "object"}, "OrMatcher": {"description": "Specifies a list of alternatives for the match.", "id": "OrMatcher", "properties": {"valueMatchers": {"items": {"$ref": "ValueMatcher"}, "type": "array"}}, "type": "object"}, "PathSegment": {"description": "Specifies the segment in a path to retrieve value from Struct.", "id": "PathSegment", "properties": {"key": {"description": "If specified, use the key to retrieve the value in a Struct.", "type": "string"}}, "type": "object"}, "PerXdsConfig": {"description": "Detailed config (per xDS) with status. [#next-free-field: 8]", "id": "PerXdsConfig", "properties": {"clientStatus": {"deprecated": true, "description": "Client config status is populated by xDS clients. Will not be present if the CSDS server is an xDS server. No matter what the client config status is, xDS clients should always dump the most recent accepted xDS config. .. attention:: This field is deprecated. Use :ref:`ClientResourceStatus ` for per-resource config status instead.", "enum": ["CLIENT_UNKNOWN", "CLIENT_REQUESTED", "CLIENT_ACKED", "CLIENT_NACKED", "CLIENT_RECEIVED_ERROR"], "enumDescriptions": ["Config status is not available/unknown.", "Client requested the config but hasn't received any config from management server yet.", "Client received the config and replied with ACK.", "Client received the config and replied with NACK. Notably, the attached config dump is not the NACKed version, but the most recent accepted one. If no config is accepted yet, the attached config dump will be empty.", "Client received an error from the control plane. The attached config dump is the most recent accepted one. If no config is accepted yet, the attached config dump will be empty."], "type": "string"}, "clusterConfig": {"$ref": "ClustersConfigDump"}, "endpointConfig": {"$ref": "EndpointsConfigDump"}, "listenerConfig": {"$ref": "ListenersConfigDump"}, "routeConfig": {"$ref": "RoutesConfigDump"}, "scopedRouteConfig": {"$ref": "ScopedRoutesConfigDump"}, "status": {"description": "Config status generated by management servers. Will not be present if the CSDS server is an xDS client.", "enum": ["UNKNOWN", "SYNCED", "NOT_SENT", "STALE", "ERROR"], "enumDescriptions": ["Status info is not available/unknown.", "Management server has sent the config to client and received ACK.", "Config is not sent.", "Management server has sent the config to client but hasn’t received ACK/NACK.", "Management server has sent the config to client but received NACK. The attached config dump will be the latest config (the rejected one), since it is the persisted version in the management server."], "type": "string"}}, "type": "object"}, "Pipe": {"id": "<PERSON><PERSON>", "properties": {"mode": {"description": "The mode for the Pipe. Not applicable for abstract sockets.", "format": "uint32", "type": "integer"}, "path": {"description": "Unix Domain Socket path. On Linux, paths starting with '@' will use the abstract namespace. The starting '@' is replaced by a null byte by <PERSON><PERSON>. Paths starting with '@' will result in an error in environments other than Linux.", "type": "string"}}, "type": "object"}, "RegexMatcher": {"description": "A regex matcher designed for safety when used with untrusted input.", "id": "RegexMatcher", "properties": {"googleRe2": {"$ref": "GoogleRE2", "deprecated": true, "description": "Google's RE2 regex engine."}, "regex": {"description": "The regex match string. The string must be supported by the configured engine. The regex is matched against the full string, not as a partial match.", "type": "string"}}, "type": "object"}, "RoutesConfigDump": {"description": "Envoy's RDS implementation fills this message with all currently loaded routes, as described by their RouteConfiguration objects. Static routes that are either defined in the bootstrap configuration or defined inline while configuring listeners are separated from those configured dynamically via RDS. Route configuration information can be used to recreate an Envoy configuration by populating all routes as static routes or by returning them in RDS responses.", "id": "RoutesConfigDump", "properties": {"dynamicRouteConfigs": {"description": "The dynamically loaded route configs.", "items": {"$ref": "DynamicRouteConfig"}, "type": "array"}, "staticRouteConfigs": {"description": "The statically loaded route configs.", "items": {"$ref": "StaticRouteConfig"}, "type": "array"}}, "type": "object"}, "ScopedRoutesConfigDump": {"description": "Envoy's scoped RDS implementation fills this message with all currently loaded route configuration scopes (defined via ScopedRouteConfigurationsSet protos). This message lists both the scopes defined inline with the higher order object (i.e., the HttpConnectionManager) and the dynamically obtained scopes via the SRDS API.", "id": "ScopedRoutesConfigDump", "properties": {"dynamicScopedRouteConfigs": {"description": "The dynamically loaded scoped route configs.", "items": {"$ref": "DynamicScopedRouteConfigs"}, "type": "array"}, "inlineScopedRouteConfigs": {"description": "The statically loaded scoped route configs.", "items": {"$ref": "InlineScopedRouteConfigs"}, "type": "array"}}, "type": "object"}, "SemanticVersion": {"description": "Envoy uses SemVer (https://semver.org/). Major/minor versions indicate expected behaviors and APIs, the patch version field is used only for security fixes and can be generally ignored.", "id": "SemanticVersion", "properties": {"majorNumber": {"format": "uint32", "type": "integer"}, "minorNumber": {"format": "uint32", "type": "integer"}, "patch": {"format": "uint32", "type": "integer"}}, "type": "object"}, "SocketAddress": {"description": "[#next-free-field: 8]", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"address": {"description": "The address for this socket. :ref:`Listeners ` will bind to the address. An empty address is not allowed. Specify ``0.0.0.0`` or ``::`` to bind to any address. [#comment:TODO(zu<PERSON>cher) reinstate when implemented: It is possible to distinguish a Listener address via the prefix/suffix matching in :ref:`FilterChainMatch `.] When used within an upstream :ref:`BindConfig `, the address controls the source address of outbound connections. For :ref:`clusters `, the cluster type determines whether the address must be an IP (``STATIC`` or ``EDS`` clusters) or a hostname resolved by DNS (``STRICT_DNS`` or ``LOGICAL_DNS`` clusters). Address resolution can be customized via :ref:`resolver_name `.", "type": "string"}, "ipv4Compat": {"description": "When binding to an IPv6 address above, this enables `IPv4 compatibility `_. Binding to ``::`` will allow both IPv4 and IPv6 connections, with peer IPv4 addresses mapped into IPv6 space as ``::FFFF:``.", "type": "boolean"}, "namedPort": {"description": "This is only valid if :ref:`resolver_name ` is specified below and the named resolver is capable of named port resolution.", "type": "string"}, "networkNamespaceFilepath": {"description": "Filepath that specifies the Linux network namespace this socket will be created in (see ``man 7 network_namespaces``). If this field is set, <PERSON>voy will create the socket in the specified network namespace. .. note:: Setting this parameter requires <PERSON>voy to run with the ``CAP_NET_ADMIN`` capability. .. note:: Currently only used for Listener sockets. .. attention:: Network namespaces are only configurable on Linux. Otherwise, this field has no effect.", "type": "string"}, "portValue": {"format": "uint32", "type": "integer"}, "protocol": {"enum": ["TCP", "UDP"], "enumDescriptions": ["", ""], "type": "string"}, "resolverName": {"description": "The name of the custom resolver. This must have been registered with Envoy. If this is empty, a context dependent default applies. If the address is a concrete IP address, no resolution will occur. If address is a hostname this should be set for resolution other than DNS. Specifying a custom resolver with ``STRICT_DNS`` or ``LOGICAL_DNS`` will generate an error at runtime.", "type": "string"}}, "type": "object"}, "StaticCluster": {"description": "Describes a statically loaded cluster.", "id": "StaticCluster", "properties": {"cluster": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The cluster config.", "type": "object"}, "lastUpdated": {"description": "The timestamp when the Cluster was last updated.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "StaticEndpointConfig": {"id": "StaticEndpointConfig", "properties": {"endpointConfig": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The endpoint config.", "type": "object"}, "lastUpdated": {"description": "[#not-implemented-hide:] The timestamp when the Endpoint was last updated.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "StaticListener": {"description": "Describes a statically loaded listener.", "id": "StaticListener", "properties": {"lastUpdated": {"description": "The timestamp when the Listener was last successfully updated.", "format": "google-datetime", "type": "string"}, "listener": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The listener config.", "type": "object"}}, "type": "object"}, "StaticRouteConfig": {"id": "StaticRouteConfig", "properties": {"lastUpdated": {"description": "The timestamp when the Route was last updated.", "format": "google-datetime", "type": "string"}, "routeConfig": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The route config.", "type": "object"}}, "type": "object"}, "StringMatcher": {"description": "Specifies the way to match a string. [#next-free-field: 9]", "id": "StringMatcher", "properties": {"contains": {"description": "The input string must have the substring specified here. .. note:: Empty contains match is not allowed, please use ``safe_regex`` instead. Examples: * ``abc`` matches the value ``xyz.abc.def``", "type": "string"}, "custom": {"$ref": "TypedExtensionConfig", "description": "Use an extension as the matcher type. [#extension-category: envoy.string_matcher]"}, "exact": {"description": "The input string must match exactly the string specified here. Examples: * ``abc`` only matches the value ``abc``.", "type": "string"}, "ignoreCase": {"description": "If ``true``, indicates the exact/prefix/suffix/contains matching should be case insensitive. This has no effect for the ``safe_regex`` match. For example, the matcher ``data`` will match both input string ``Data`` and ``data`` if this option is set to ``true``.", "type": "boolean"}, "prefix": {"description": "The input string must have the prefix specified here. .. note:: Empty prefix match is not allowed, please use ``safe_regex`` instead. Examples: * ``abc`` matches the value ``abc.xyz``", "type": "string"}, "safeRegex": {"$ref": "RegexMatcher", "description": "The input string must match the regular expression specified here."}, "suffix": {"description": "The input string must have the suffix specified here. .. note:: Empty suffix match is not allowed, please use ``safe_regex`` instead. Examples: * ``abc`` matches the value ``xyz.abc``", "type": "string"}}, "type": "object"}, "StructMatcher": {"description": "StructMatcher provides a general interface to check if a given value is matched in google.protobuf.Struct. It uses ``path`` to retrieve the value from the struct and then check if it's matched to the specified value. For example, for the following Struct: .. code-block:: yaml fields: a: struct_value: fields: b: struct_value: fields: c: string_value: pro t: list_value: values: - string_value: m - string_value: n The following MetadataMatcher is matched as the path [a, b, c] will retrieve a string value \"pro\" from the Metadata which is matched to the specified prefix match. .. code-block:: yaml path: - key: a - key: b - key: c value: string_match: prefix: pr The following StructMatcher is matched as the code will match one of the string values in the list at the path [a, t]. .. code-block:: yaml path: - key: a - key: t value: list_match: one_of: string_match: exact: m An example use of StructMatcher is to match metadata in envoy.v*.core.Node.", "id": "StructMatcher", "properties": {"path": {"description": "The path to retrieve the Value from the Struct.", "items": {"$ref": "PathSegment"}, "type": "array"}, "value": {"$ref": "ValueMatcher", "description": "The StructMatcher is matched if the value retrieved by path is matched to this value."}}, "type": "object"}, "TypedExtensionConfig": {"description": "Message type for extension configuration.", "id": "TypedExtensionConfig", "properties": {"name": {"description": "The name of an extension. This is not used to select the extension, instead it serves the role of an opaque identifier.", "type": "string"}, "typedConfig": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The typed config for the extension. The type URL will be used to identify the extension. In the case that the type URL is *xds.type.v3.TypedStruct* (or, for historical reasons, *udpa.type.v1.TypedStruct*), the inner type URL of *TypedStruct* will be utilized. See the :ref:`extension configuration overview ` for further details.", "type": "object"}}, "type": "object"}, "UpdateFailureState": {"id": "UpdateFailureState", "properties": {"details": {"description": "Details about the last failed update attempt.", "type": "string"}, "failedConfiguration": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "What the component configuration would have been if the update had succeeded. This field may not be populated by xDS clients due to storage overhead.", "type": "object"}, "lastUpdateAttempt": {"description": "Time of the latest failed update attempt.", "format": "google-datetime", "type": "string"}, "versionInfo": {"description": "This is the version of the rejected resource. [#not-implemented-hide:]", "type": "string"}}, "type": "object"}, "ValueMatcher": {"description": "Specifies the way to match a ProtobufWkt::Value. Primitive values and ListValue are supported. StructValue is not supported and is always not matched. [#next-free-field: 8]", "id": "ValueMatcher", "properties": {"boolMatch": {"description": "If specified, a match occurs if and only if the target value is a bool value and is equal to this field.", "type": "boolean"}, "doubleMatch": {"$ref": "DoubleM<PERSON>er", "description": "If specified, a match occurs if and only if the target value is a double value and is matched to this field."}, "listMatch": {"$ref": "ListMatcher", "description": "If specified, a match occurs if and only if the target value is a list value and is matched to this field."}, "nullMatch": {"$ref": "NullMatch", "description": "If specified, a match occurs if and only if the target value is a NullValue."}, "orMatch": {"$ref": "OrMatcher", "description": "If specified, a match occurs if and only if any of the alternatives in the match accept the value."}, "presentMatch": {"description": "If specified, value match will be performed based on whether the path is referring to a valid primitive value in the metadata. If the path is referring to a non-primitive value, the result is always not matched.", "type": "boolean"}, "stringMatch": {"$ref": "StringMatcher", "description": "If specified, a match occurs if and only if the target value is a string value and is matched to this field."}}, "type": "object"}}, "servicePath": "", "title": "Traffic Director API", "version": "v3", "version_module": true}